#!/usr/bin/env python3
"""
修复JSONL文件中的Unicode编码问题
将\\uXXXX格式的Unicode转义序列转换为正常的中文字符
"""

import json
import os
import sys

def fix_unicode_in_jsonl(input_file, output_file=None):
    """
    修复JSONL文件中的Unicode编码问题
    
    Args:
        input_file (str): 输入文件路径
        output_file (str): 输出文件路径，如果为None则覆盖原文件
    """
    
    if output_file is None:
        output_file = input_file.replace('.jsonl', '_fixed.jsonl')
    
    try:
        print(f"正在处理文件: {input_file}")
        
        fixed_lines = []
        total_lines = 0
        
        # 读取原文件
        with open(input_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                
                total_lines += 1
                
                try:
                    # 解析JSON
                    data = json.loads(line)
                    
                    # 重新序列化，确保中文字符正常显示
                    fixed_line = json.dumps(data, ensure_ascii=False, separators=(',', ':'))
                    fixed_lines.append(fixed_line)
                    
                    if line_num % 1000 == 0:
                        print(f"已处理 {line_num} 行...")
                        
                except json.JSONDecodeError as e:
                    print(f"第 {line_num} 行JSON解析错误: {e}")
                    print(f"问题行内容: {line[:100]}...")
                    continue
                except Exception as e:
                    print(f"第 {line_num} 行处理错误: {e}")
                    continue
        
        # 写入修复后的文件
        with open(output_file, 'w', encoding='utf-8') as f:
            for line in fixed_lines:
                f.write(line + '\n')
        
        print(f"\n✅ 修复完成!")
        print(f"📊 统计信息:")
        print(f"   - 总行数: {total_lines}")
        print(f"   - 成功修复: {len(fixed_lines)}")
        print(f"   - 输出文件: {output_file}")
        
        # 显示修复前后的对比示例
        if fixed_lines:
            print(f"\n📋 修复效果预览:")
            print("修复前 (原始):")
            with open(input_file, 'r', encoding='utf-8') as f:
                first_line = f.readline().strip()
                print(f"  {first_line[:200]}...")
            
            print("修复后:")
            print(f"  {fixed_lines[0][:200]}...")
        
        return output_file
        
    except FileNotFoundError:
        print(f"❌ 错误: 文件 {input_file} 不存在")
        return None
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")
        return None

def preview_file(file_path, num_lines=3):
    """
    预览文件内容
    
    Args:
        file_path (str): 文件路径
        num_lines (int): 预览行数
    """
    try:
        print(f"\n📖 预览文件: {file_path}")
        print("=" * 50)
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if i >= num_lines:
                    break
                
                line = line.strip()
                if line:
                    try:
                        data = json.loads(line)
                        print(f"第 {i+1} 行:")
                        
                        # 显示主要字段
                        if 'id' in data:
                            print(f"  ID: {data['id']}")
                        
                        if 'conversations' in data and data['conversations']:
                            conv = data['conversations'][0]
                            if 'value' in conv:
                                value = conv['value'][:100]
                                print(f"  内容: {value}...")
                        
                        print()
                        
                    except json.JSONDecodeError:
                        print(f"第 {i+1} 行: JSON格式错误")
                        print(f"  原始内容: {line[:100]}...")
                        print()
                        
    except Exception as e:
        print(f"预览文件时出错: {e}")

def main():
    """主函数"""
    print("JSONL Unicode编码修复工具")
    print("=" * 50)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    else:
        # 默认处理当前目录下的文件
        input_file = "chemvlm/mm_pure_fix.jsonl"
    
    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"❌ 文件不存在: {input_file}")
        
        # 尝试查找可能的文件
        possible_files = []
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith('.jsonl') and 'mm_pure' in file:
                    possible_files.append(os.path.join(root, file))
        
        if possible_files:
            print(f"\n💡 找到可能的文件:")
            for i, file in enumerate(possible_files, 1):
                print(f"  {i}. {file}")
            
            try:
                choice = input(f"\n请选择文件 (1-{len(possible_files)}) 或按Enter退出: ").strip()
                if choice and choice.isdigit():
                    choice_idx = int(choice) - 1
                    if 0 <= choice_idx < len(possible_files):
                        input_file = possible_files[choice_idx]
                    else:
                        print("无效选择，退出")
                        return
                else:
                    print("退出")
                    return
            except KeyboardInterrupt:
                print("\n退出")
                return
        else:
            print("未找到相关文件")
            return
    
    # 预览原文件
    print(f"\n📋 原文件预览:")
    preview_file(input_file, 2)
    
    # 修复文件
    output_file = fix_unicode_in_jsonl(input_file)
    
    if output_file:
        # 预览修复后的文件
        print(f"\n📋 修复后文件预览:")
        preview_file(output_file, 2)
        
        # 询问是否替换原文件
        try:
            replace = input(f"\n是否用修复后的文件替换原文件? (y/N): ").strip().lower()
            if replace in ['y', 'yes']:
                import shutil
                shutil.move(output_file, input_file)
                print(f"✅ 已替换原文件: {input_file}")
            else:
                print(f"✅ 修复后的文件保存为: {output_file}")
        except KeyboardInterrupt:
            print(f"\n✅ 修复后的文件保存为: {output_file}")

if __name__ == "__main__":
    main()
