import os
import pandas as pd
import pyarrow.ipc as ipc

def load_and_save_mmmu_data(base_path, output_path):
    """
    Loads all .arrow files from the subdirectories of the MMMU dataset,
    and saves the combined dataframes as CSV and JSON files.

    Args:
        base_path (str): The base path to the MMMU dataset.
        output_path (str): The path to the directory where output files will be saved.
    """
    if not os.path.exists(output_path):
        os.makedirs(output_path)

    subfolders = ['standard (10 options)', 'standard (4 options)', 'vision']

    for subfolder in subfolders:
        data_path = os.path.join(base_path, subfolder)
        
        all_files = []
        for root, _, files in os.walk(data_path):
            for file in files:
                if file.endswith('.arrow'):
                    all_files.append(os.path.join(root, file))

        if not all_files:
            print(f"No .arrow files found in {data_path}")
            continue

        df_list = []
        for f in all_files:
            with ipc.open_stream(f) as reader:
                df_list.append(reader.read_pandas())

        df = pd.concat(df_list, ignore_index=True)

        print(f"--- Data for {subfolder} ---")
        print(f"Shape: {df.shape}")
        print(df.head())
        print("\n")

        # Sanitize the subfolder name to create a valid filename
        safe_filename = subfolder.replace(' ', '_').replace('(', '').replace(')', '')
        
        # Save to CSV
        csv_path = os.path.join(output_path, f"{safe_filename}.csv")
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"Saved CSV to {csv_path}")

        # Save to JSON
        json_path = os.path.join(output_path, f"{safe_filename}.json")
        df.to_json(json_path, orient='records', lines=True, force_ascii=False)
        print(f"Saved JSON to {json_path}\n")


if __name__ == '__main__':
    base_mmmu_path = r'E:\zxy_llm\benchmark\BENCHMARK_DATA\MMMU\MMMU___mmmu_pro'
    output_dir = r'E:\zxy_llm\benchmark\BENCHMARK_DATA\自用脚本\output'
    load_and_save_mmmu_data(base_mmmu_path, output_dir)
