# Parquet 文件通用读取器

这是一个通用的 Python 脚本，用于读取、分析和交互式查询 Parquet 格式的数据文件。

## 功能特性

- **基本信息展示**: 显示 Parquet 文件的基本元数据，包括数据形状、列名、数据类型等。
- **数据预览**: 快速查看文件的前 5 行数据。
- **深度分析**: 对数据进行初步探索，包括计算每列的唯一值数量、统计缺失值等。
- **样本数据导出**: 自动将文件的前 100 行保存为一个同名的 `.csv` 样本文件（例如，输入 `data.parquet` 将生成 `data_sample.csv`）。
- **交互式查询**: 进入一个交互式模式，允许用户按行号或行号范围实时查询数据。

## 依赖要求

在运行此脚本前，请确保您已安装以下 Python 库：

- `pandas`
- `pyarrow`

您可以使用 pip 进行安装：
```bash
pip install pandas pyarrow
```

## 使用方法

通过命令行来运行此脚本，并提供一个 Parquet 文件的路径作为参数。

### 命令格式
```bash
python read_parquet.py <您的 Parquet 文件路径>
```

### 使用示例

假设 `read_parquet.py` 脚本位于 `自用脚本` 文件夹下，而您想分析的 Parquet 文件位于 `MMLU-Pro` 文件夹下，您可以这样运行：

```bash
# 从“自用脚本”文件夹内执行
python read_parquet.py ..\MMLU-Pro\test-00000-of-00001.parquet
```

### 交互式查询

脚本运行并显示基本信息后，会自动进入交互式查询模式。

- **查询单行**: 输入一个数字，例如 `5`，然后按 Enter，即可查看第 5 行的数据。
- **查询范围**: 输入一个范围，例如 `10-15`，然后按 Enter，即可查看第 10 到 15 行的数据。
- **退出**: 输入 `quit`，然后按 Enter，即可退出程序。
